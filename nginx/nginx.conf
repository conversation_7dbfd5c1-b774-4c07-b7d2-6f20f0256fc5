# Nginx 메인 설정
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 로그 형식
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    access_log /var/log/nginx/access.log main;

    # 기본 설정
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # Gzip 압축 설정
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        application/atom+xml
        application/geo+json
        application/javascript
        application/x-javascript
        application/json
        application/ld+json
        application/manifest+json
        application/rdf+xml
        application/rss+xml
        application/xhtml+xml
        application/xml
        font/eot
        font/otf
        font/ttf
        image/svg+xml
        text/css
        text/javascript
        text/plain
        text/xml;

    # Rate Limiting 설정
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=resume_limit:10m rate=5r/m;
    limit_conn_zone $binary_remote_addr zone=conn_limit:10m;

    # 업스트림 백엔드 서버
    upstream fastapi_backend {
        least_conn;
        server fastapi_app:8000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # HTTP 메인 서버
    server {
        listen 80;
        server_name localhost;

        # 보안 헤더 (SSL 제외)
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

        # Rate Limiting 적용
        limit_req zone=api_limit burst=20 nodelay;
        limit_conn conn_limit 20;

        # 클라이언트 설정
        client_max_body_size 1M;
        client_body_timeout 60s;
        client_header_timeout 60s;

        # API 엔드포인트
        location /api/ {
            # 이력서 분석 엔드포인트에 특별 제한
            location /api/v1/resume/analyze {
                limit_req zone=resume_limit burst=2 nodelay;
                proxy_pass http://fastapi_backend;
                
                # 프록시 헤더 설정
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                # 타임아웃 설정 (AI 처리 시간 고려)
                proxy_connect_timeout 10s;
                proxy_send_timeout 60s;
                proxy_read_timeout 60s;
            }

            location /api/v1/resume/analyze-async {
                limit_req zone=resume_limit burst=5 nodelay;
                proxy_pass http://fastapi_backend;
                
                # 프록시 헤더 설정
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            # 기본 API 엔드포인트
            proxy_pass http://fastapi_backend;
            
            # 프록시 헤더 설정
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 헬스 체크
        location /health {
            proxy_pass http://fastapi_backend;
            
            # 프록시 헤더 설정
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            access_log off;
        }

        # API 문서 (개발 환경에서만)
        location /docs {
            proxy_pass http://fastapi_backend;
            
            # 프록시 헤더 설정
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /redoc {
            proxy_pass http://fastapi_backend;
            
            # 프록시 헤더 설정
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # OpenAPI 스키마
        location /openapi.json {
            proxy_pass http://fastapi_backend;
            
            # 프록시 헤더 설정
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 404 처리
        location / {
            return 404 '{"error": "Not Found", "status_code": 404}';
            add_header Content-Type application/json;
        }

        # 에러 페이지
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }

    # Flower 모니터링 (개발 환경)
    server {
        listen 5555;
        server_name localhost;

        location / {
            proxy_pass http://jobkorea_celery_flower:5555;
            
            # 프록시 헤더 설정
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
