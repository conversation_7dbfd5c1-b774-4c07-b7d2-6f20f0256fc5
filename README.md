# JobKorea AI Challenge - 구직자 맞춤형 면접 준비 서비스

구직자의 이력서 정보를 분석하여 맞춤형 면접 질문과 학습 경로를 제공하는 AI 기반 RESTful API 서비스입니다.

## 🚀 주요 기능

- **이력서 분석**: 텍스트 기반 이력서 정보 입력 및 구조화
- **AI 면접 질문 생성**: Gemini AI를 활용한 맞춤형 면접 질문 5개 생성
- **개인화된 학습 경로**: 구체적 실천 방안을 포함한 학습 로드맵 제안
- **Fast/Async Lane**: 동기/비동기 처리 지원
- **Rate Limiting**: API 남용 방지 및 안정적 서비스 제공

## 🏗️ 시스템 아키텍처

```
Client
  │
  ▼
Nginx  ──(TLS, rate limit, gzip, SSE buffering off)
  │
  ▼
FastAPI (Gunicorn+Uvicorn workers)
  ├─▶ [Fast-lane]  Gemini  (timeout+circuit breaker, streaming)
  ├─▶ [Async-lane]  Redis(broker) → Celery workers → Gemini
  ├─▶ Redis (result backend + cache + idempotency + rate limit tokens)
  └─▶ Postgres (profiles, prompts, transcripts, metrics logs)
               └─ DLQ(Dead-letter): RabbitMQ queue or Redis Stream
```

## 🛠️ 기술 스택

- **Backend**: FastAPI, Python 3.11
- **AI/LLM**: Google Gemini Pro
- **Database**: PostgreSQL 16
- **Cache/Queue**: Redis 7, RabbitMQ 3
- **Async Processing**: Celery
- **Web Server**: Nginx (리버스 프록시)
- **Container**: Docker & Docker Compose
- **Monitoring**: Flower (Celery 모니터링)

## 📦 설치 및 실행

### 1. 사전 요구사항

- Docker & Docker Compose
- Gemini API 키

### 2. 환경 설정

```bash
# 저장소 클론
git clone <repository-url>
cd JobKorea-AI-Challenge

# 환경 변수 파일 생성
cp env.example .env
```

`.env` 파일에서 다음 값을 설정하세요:

```bash
# 필수: Gemini API 키
GEMINI_API_KEY=your_gemini_api_key_here

# 선택사항: 기타 설정값들은 기본값 사용 가능
SECRET_KEY=your-secret-key-here
```

### 3. SSL 인증서 생성 (개발용)

```bash
chmod +x nginx/ssl/generate_cert.sh
docker run --rm -v $(pwd)/nginx/ssl:/etc/nginx/ssl alpine/openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout /etc/nginx/ssl/key.pem -out /etc/nginx/ssl/cert.pem -subj "/C=KR/ST=Seoul/L=Seoul/O=JobKorea/CN=localhost"
```

### 4. 서비스 실행

```bash
# 전체 서비스 시작
docker-compose up -d

# 로그 확인
docker-compose logs -f

# 특정 서비스 로그 확인
docker-compose logs -f fastapi_app
docker-compose logs -f celery_worker
```

### 5. 서비스 접속

- **API 문서**: https://localhost/docs
- **Flower 모니터링**: http://localhost:5555
- **API 엔드포인트**: https://localhost/api/v1/

## 📋 API 사용법

### 동기 처리 (Fast Lane)

```bash
curl -X POST "https://localhost/api/v1/resume/analyze" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: your-session-id" \
  -d '{
    "resume_text": "3년차 백엔드 개발자, Spring Boot/MSA/Python 기반 커머스 서비스 개발, AWS EC2 운영 경험"
  }'
```

### 비동기 처리 (Async Lane)

```bash
# 1. 비동기 작업 시작
curl -X POST "https://localhost/api/v1/resume/analyze-async" \
  -H "Content-Type: application/json" \
  -d '{
    "resume_text": "3년차 백엔드 개발자, Spring Boot/MSA/Python 기반 커머스 서비스 개발, AWS EC2 운영 경험"
  }'

# 응답: {"job_id": "abc123", "status": "PENDING", ...}

# 2. 작업 상태 확인
curl "https://localhost/api/v1/resume/job-status/abc123"
```

### 응답 예시

```json
{
  "session_id": "uuid-string",
  "resume_profile_id": 1,
  "interview_questions": [
    {
      "question_text": "Spring Boot에서 MSA 구현 시 서비스 간 통신은 어떻게 처리하셨나요?",
      "category": "기술역량",
      "difficulty": "중급",
      "reasoning": "MSA 경험을 바탕으로 실제 구현 경험을 확인하는 질문"
    }
  ],
  "learning_paths": [
    {
      "title": "MSA 아키텍처 심화",
      "description": "마이크로서비스 아키텍처 설계 및 운영 전문성 강화",
      "priority": "높음",
      "estimated_duration": "2-3개월",
      "difficulty": "고급",
      "action_items": [
        {
          "step": 1,
          "action": "분산 시스템 설계 패턴 학습",
          "duration": "2주",
          "resources": ["도서", "온라인강의"]
        }
      ],
      "recommended_resources": [
        {
          "type": "도서",
          "title": "마이크로서비스 패턴",
          "description": "MSA 설계 및 구현 가이드"
        }
      ],
      "expected_outcomes": [
        "MSA 설계 능력 향상",
        "분산 시스템 이해도 증진"
      ]
    }
  ],
  "created_at": "2024-01-01T12:00:00Z"
}
```

## 🔧 개발 환경 설정

### 로컬 개발

```bash
# Python 가상환경 생성
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate  # Windows

# 의존성 설치
pip install -r requirements.txt

# 개발 서버 실행
cd app
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 데이터베이스 마이그레이션

```bash
# Alembic 초기화 (최초 1회)
alembic init alembic

# 마이그레이션 파일 생성
alembic revision --autogenerate -m "Initial migration"

# 마이그레이션 적용
alembic upgrade head
```

## 📊 모니터링

### Flower (Celery 모니터링)

- URL: http://localhost:5555
- Celery 작업 상태, 워커 상태, 작업 히스토리 등 확인

### 로그 확인

```bash
# 전체 로그
docker-compose logs -f

# 특정 서비스 로그
docker-compose logs -f fastapi_app
docker-compose logs -f celery_worker
docker-compose logs -f postgres
docker-compose logs -f redis
```

### 헬스 체크

```bash
# API 헬스 체크
curl https://localhost/health

# Celery 워커 헬스 체크
# Flower UI에서 확인 가능
```

## 🔒 보안

- **HTTPS**: 모든 API 통신 암호화
- **Rate Limiting**: API 요청 제한
- **Input Validation**: Pydantic을 통한 입력 검증
- **CORS**: 적절한 CORS 정책 적용
- **Security Headers**: 보안 헤더 설정

## 🧪 테스트

```bash
# 단위 테스트 실행
pytest tests/

# 특정 테스트 실행
pytest tests/test_api.py -v

# 커버리지 확인
pytest --cov=app tests/
```

## 📈 성능 최적화

- **Connection Pooling**: 데이터베이스 연결 풀링
- **Redis Caching**: 결과 캐싱
- **Gzip Compression**: 응답 데이터 압축
- **Keep-Alive**: HTTP 연결 유지
- **Circuit Breaker**: 외부 서비스 장애 대응

## 🚨 트러블슈팅

### 일반적인 문제

1. **Gemini API 오류**
   ```bash
   # API 키 확인
   echo $GEMINI_API_KEY
   
   # 로그 확인
   docker-compose logs fastapi_app | grep -i gemini
   ```

2. **데이터베이스 연결 실패**
   ```bash
   # PostgreSQL 상태 확인
   docker-compose ps postgres
   
   # 연결 테스트
   docker-compose exec postgres psql -U jobkorea -d jobkorea_ai -c "SELECT 1;"
   ```

3. **Redis 연결 실패**
   ```bash
   # Redis 상태 확인
   docker-compose ps redis
   
   # Redis 연결 테스트
   docker-compose exec redis redis-cli ping
   ```

4. **Celery 워커 문제**
   ```bash
   # 워커 상태 확인
   docker-compose ps celery_worker
   
   # 워커 로그 확인
   docker-compose logs celery_worker
   ```

### 로그 레벨 조정

`.env` 파일에서 `LOG_LEVEL` 조정:
```bash
LOG_LEVEL=DEBUG  # 상세 로그
LOG_LEVEL=INFO   # 일반 로그 (기본값)
LOG_LEVEL=ERROR  # 오류만
```

## 📝 라이선스

이 프로젝트는 MIT 라이선스 하에 배포됩니다.

## 🤝 기여

1. Fork the Project
2. Create your Feature Branch (`git checkout -b feature/AmazingFeature`)
3. Commit your Changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the Branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📞 지원

문제가 발생하거나 질문이 있으시면 이슈를 생성해주세요.

---

**JobKorea AI Challenge** - 구직자의 성공적인 취업을 위한 AI 면접 준비 서비스
