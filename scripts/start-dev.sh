#!/bin/bash

# 개발 환경 시작 스크립트

echo "🚀 JobKorea AI Challenge 개발 환경을 시작합니다..."

# .env 파일 확인
if [ ! -f .env ]; then
    echo "❌ .env 파일이 없습니다. env.example을 참고해서 .env 파일을 생성해주세요."
    exit 1
fi

# GEMINI_API_KEY 확인
if ! grep -q "GEMINI_API_KEY=" .env; then
    echo "❌ .env 파일에 GEMINI_API_KEY가 설정되지 않았습니다."
    exit 1
fi

# Docker Compose 실행
echo "🐳 Docker 컨테이너를 시작합니다..."
docker-compose -f docker-compose.dev.yml up --build -d

# 컨테이너 상태 확인
echo "⏳ 컨테이너가 준비될 때까지 기다립니다..."
sleep 10

# 데이터베이스 마이그레이션
echo "🗄️ 데이터베이스 마이그레이션을 실행합니다..."
docker exec jobkorea_fastapi_dev alembic upgrade head

echo "✅ 개발 환경이 준비되었습니다!"
echo "📱 API 문서: http://localhost:8000/docs"
echo "🔍 헬스체크: http://localhost:8000/health"
echo "📊 로그 확인: docker logs -f jobkorea_fastapi_dev"
