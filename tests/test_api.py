"""
API 엔드포인트 테스트
"""
import pytest
from httpx import AsyncClient
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)


def test_health_check():
    """헬스 체크 테스트"""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"


def test_resume_analyze_validation():
    """이력서 분석 입력 검증 테스트"""
    # 빈 텍스트
    response = client.post(
        "/api/v1/resume/analyze",
        json={"resume_text": ""}
    )
    assert response.status_code == 422
    
    # 너무 짧은 텍스트
    response = client.post(
        "/api/v1/resume/analyze", 
        json={"resume_text": "짧음"}
    )
    assert response.status_code == 422
    
    # 너무 긴 텍스트
    long_text = "a" * 2001
    response = client.post(
        "/api/v1/resume/analyze",
        json={"resume_text": long_text}
    )
    assert response.status_code == 422


@pytest.mark.asyncio
async def test_resume_analyze_async_endpoint():
    """비동기 이력서 분석 엔드포인트 테스트"""
    async with Async<PERSON><PERSON>(app=app, base_url="http://test") as ac:
        response = await ac.post(
            "/api/v1/resume/analyze-async",
            json={
                "resume_text": "3년차 백엔드 개발자, Spring Boot/Python 기반 서비스 개발 경험"
            }
        )
        
        # API 키가 없으면 500 오류가 날 수 있지만, 구조적으로는 정상
        assert response.status_code in [202, 500]
        
        if response.status_code == 202:
            data = response.json()
            assert "job_id" in data
            assert data["status"] == "PENDING"


def test_invalid_job_status():
    """존재하지 않는 작업 ID 조회 테스트"""
    response = client.get("/api/v1/resume/job-status/invalid-job-id")
    # Celery가 없으면 500 오류가 날 수 있음
    assert response.status_code in [200, 500]


def test_invalid_profile_id():
    """존재하지 않는 프로필 ID 조회 테스트"""
    response = client.get("/api/v1/resume/profile/99999")
    assert response.status_code == 404
    assert "프로필을 찾을 수 없습니다" in response.json()["detail"]
