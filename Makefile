# JobKorea AI Challenge Makefile

.PHONY: help build up down logs clean test lint format ssl-cert

# 기본 타겟
help:
	@echo "Available commands:"
	@echo "  build      - Docker 이미지 빌드"
	@echo "  up         - 서비스 시작"
	@echo "  down       - 서비스 중지"
	@echo "  logs       - 로그 확인"
	@echo "  clean      - 정리 (컨테이너, 볼륨, 이미지 삭제)"
	@echo "  test       - 테스트 실행"
	@echo "  lint       - 코드 린팅"
	@echo "  format     - 코드 포맷팅"
	@echo "  ssl-cert   - SSL 인증서 생성"

# Docker 명령어
build:
	docker-compose build

up:
	make ssl-cert
	docker-compose up -d
	@echo "서비스가 시작되었습니다."
	@echo "API 문서: https://localhost/docs"
	@echo "Flower 모니터링: http://localhost:5555"

down:
	docker-compose down

logs:
	docker-compose logs -f

restart:
	docker-compose restart

# 정리
clean:
	docker-compose down -v --rmi all
	docker system prune -f

# SSL 인증서 생성
ssl-cert:
	@if [ ! -f nginx/ssl/cert.pem ]; then \
		echo "SSL 인증서 생성 중..."; \
		docker run --rm -v $(PWD)/nginx/ssl:/etc/nginx/ssl alpine/openssl \
		req -x509 -nodes -days 365 -newkey rsa:2048 \
		-keyout /etc/nginx/ssl/key.pem \
		-out /etc/nginx/ssl/cert.pem \
		-subj "/C=KR/ST=Seoul/L=Seoul/O=JobKorea/CN=localhost"; \
		echo "SSL 인증서가 생성되었습니다."; \
	fi

# 개발 도구
test:
	docker-compose exec fastapi_app pytest tests/ -v

lint:
	docker-compose exec fastapi_app flake8 app/
	docker-compose exec fastapi_app mypy app/

format:
	docker-compose exec fastapi_app black app/

# 데이터베이스
db-migrate:
	docker-compose exec fastapi_app alembic upgrade head

db-reset:
	docker-compose exec postgres psql -U jobkorea -d jobkorea_ai -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"
	make db-migrate

# 모니터링
monitor:
	@echo "시스템 상태 확인..."
	docker-compose ps
	@echo "\n헬스 체크..."
	curl -k https://localhost/health || echo "API 서버가 응답하지 않습니다."

# 로컬 개발
dev-setup:
	python -m venv venv
	. venv/bin/activate && pip install -r requirements.txt
	@echo "로컬 개발 환경이 설정되었습니다."
	@echo "활성화: source venv/bin/activate"

dev-run:
	cd app && uvicorn main:app --reload --host 0.0.0.0 --port 8000

# 환경 파일 체크
check-env:
	@if [ ! -f .env ]; then \
		echo "⚠️  .env 파일이 없습니다. env.example을 참고하여 생성하세요."; \
		exit 1; \
	fi
	@if ! grep -q "GEMINI_API_KEY=" .env; then \
		echo "⚠️  GEMINI_API_KEY가 설정되지 않았습니다."; \
		exit 1; \
	fi
	@echo "✅ 환경 설정이 올바릅니다."

# 전체 실행 (처음 사용자용)
first-run: check-env ssl-cert build up
	@echo "🚀 JobKorea AI Challenge 서비스가 시작되었습니다!"
	@echo "📚 API 문서: https://localhost/docs"
	@echo "📊 Celery 모니터링: http://localhost:5555"
	@echo "🔧 로그 확인: make logs"
