"""
Gemini AI 통합 서비스 - google-genai 안전 모드
- response_schema 미사용(추후 재도입)
- contents=[prompt] 호출
- 멀티파트 안전 추출 + JSO<PERSON> 가드 + 백업 파서
- 제로-컨피그 우선, 문제 키 자동 배제 전략
"""

from typing import List, Dict, Any, Optional
import json
import asyncio
import re
import traceback

from pydantic import BaseModel, Field  # 선택: 파싱 후 검증용
from tenacity import retry, stop_after_attempt, wait_exponential
import structlog

from app.core.config import settings

from google import genai
from google.genai import types as gat

logger = structlog.get_logger()


# ---------- (선택) Pydantic 모델: 파싱 후 유효성 검사용 ----------
class Question(BaseModel):
    question_text: str
    category: str
    difficulty: str
    reasoning: str

class QuestionsPayload(BaseModel):
    questions: List[Question] = Field(default_factory=list)

class ActionItem(BaseModel):
    step: int
    action: str
    duration: str
    resources: List[str] = Field(default_factory=list)

class ResourceItem(BaseModel):
    type: str
    title: str
    url: Optional[str] = None
    description: Optional[str] = None

class LearningPath(BaseModel):
    title: str
    description: str
    priority: str
    estimated_duration: str
    difficulty: str
    action_items: List[ActionItem] = Field(default_factory=list)
    recommended_resources: List[ResourceItem] = Field(default_factory=list)
    expected_outcomes: List[str] = Field(default_factory=list)

class LearningPathsPayload(BaseModel):
    learning_paths: List[LearningPath] = Field(default_factory=list)


class GeminiService:
    """
    response_schema를 쓰지 않는 안전 모드:
      - config 없이 우선 호출(제로-컨피그)
      - 실패 시 최소 옵션부터 하나씩 추가하며 문제 키 자동 배제
      - 멀티파트/비단순 응답 안전 추출 + JSON 가드 + 백업 파서
    """

    def __init__(self):
        self.client = genai.Client(api_key=settings.gemini_api_key)

        # 추가 옵션 후보(문제 키가 있으면 자동 제외)
        self._cfg_steps = [
            {"max_output_tokens": 2048},
            {"temperature": 0.7},
            {"top_p": 0.8},
            {"top_k": 40},
            {"response_mime_type": "application/json"},
        ]

    # -------- 공용 호출기: 제로-컨피그 → 점진 옵션 추가 --------
    async def _safe_call(self, prompt: str) -> Any:
        # 1) 제로-컨피그
        try:
            return await asyncio.to_thread(
                self.client.models.generate_content,
                model="gemini-2.5-flash",
                contents=[prompt],  # 리스트로 고정
            )
        except Exception as e:
            logger.warning("Gemini zero-config 실패, 최소 옵션으로 시도",
                           err_type=type(e).__name__, err=str(e), tb=traceback.format_exc())

        # 2) 최소 옵션부터 하나씩 추가(문제 키 자동 배제)
        cfg_kwargs: Dict[str, Any] = {}
        last_error: Optional[Exception] = None

        for addition in self._cfg_steps:
            cfg_kwargs.update(addition)
            try:
                gen_cfg = gat.GenerateContentConfig(**cfg_kwargs)
            except TypeError as e:
                logger.warning("GenerateContentConfig TypeError → 해당 키 제외",
                               bad_keys=list(addition.keys()), err=str(e))
                for k in addition.keys():
                    cfg_kwargs.pop(k, None)
                last_error = e
                continue

            try:
                resp = await asyncio.to_thread(
                    self.client.models.generate_content,
                    model="gemini-2.5-flash",
                    contents=[prompt],
                    config=gen_cfg,
                )
                logger.info("Gemini 호출 성공(옵션 조합)", cfg_kwargs=cfg_kwargs)
                return resp
            except Exception as e:
                logger.warning("Gemini 호출 실패(옵션 조합) → 해당 키 제외",
                               bad_keys=list(addition.keys()),
                               err_type=type(e).__name__, err=str(e))
                for k in addition.keys():
                    cfg_kwargs.pop(k, None)
                last_error = e

        raise last_error or RuntimeError("Gemini 호출 실패: 모든 config 조합 실패")

    # ---------- Public APIs ----------
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def generate_interview_questions(
        self,
        career_summary: str,
        job_functions: str,
        technical_skills: str,
    ) -> List[Dict[str, Any]]:
        prompt = self._create_interview_questions_prompt(career_summary, job_functions, technical_skills)

        resp = await self._safe_call(prompt)

        text = self._extract_response_text(resp)
        logger.info("면접 질문 생성 완료", text_length=len(text), text_preview=text[:200])

        cleaned = self._clean_json_response(text)
        if not isinstance(cleaned, str) or not cleaned or cleaned[0] not in "{[":
            logger.warning("JSON 시작 토큰 없음. 백업 파서 사용",
                           genai_json_ok=0, genai_backup_parse_used=1, sample=cleaned[:160])
            return self._parse_text_response_to_questions(text)

        try:
            data = json.loads(cleaned)
        except json.JSONDecodeError as e:
            logger.error("면접 질문 JSON 파싱 실패", error=str(e),
                         response_text_length=len(text),
                         response_text_head=cleaned[:500],
                         response_text_tail=cleaned[-500:],
                         genai_json_ok=0, genai_backup_parse_used=1)
            return self._parse_text_response_to_questions(text)

        # (선택) Pydantic 검증
        try:
            payload = QuestionsPayload.model_validate(data)
            logger.info("면접 질문 파싱 완료",
                        question_count=len(payload.questions),
                        genai_json_ok=1, genai_backup_parse_used=0)
            return [q.model_dump() for q in payload.questions]
        except Exception as e:
            logger.warning("면접 질문 Pydantic 검증 실패 - 원시 JSON 사용",
                           err=str(e), genai_json_ok=1, genai_backup_parse_used=0)
            return data.get("questions", [])

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def generate_learning_path(
        self,
        career_summary: str,
        job_functions: str,
        technical_skills: str,
    ) -> List[Dict[str, Any]]:
        prompt = self._create_learning_path_prompt(career_summary, job_functions, technical_skills)

        resp = await self._safe_call(prompt)

        text = self._extract_response_text(resp)
        logger.info("학습 경로 생성 완료", text_length=len(text), text_preview=text[:200])

        cleaned = self._clean_json_response(text)
        if not isinstance(cleaned, str) or not cleaned or cleaned[0] not in "{[":
            logger.warning("JSON 시작 토큰 없음. 백업 파서 사용",
                           genai_json_ok=0, genai_backup_parse_used=1, sample=cleaned[:160])
            return self._parse_text_response_to_learning_paths(text)

        try:
            data = json.loads(cleaned)
        except json.JSONDecodeError as e:
            logger.error("학습 경로 JSON 파싱 실패", error=str(e),
                         response_text_length=len(text),
                         response_text_head=cleaned[:500],
                         response_text_tail=cleaned[-500:],
                         genai_json_ok=0, genai_backup_parse_used=1)
            return self._parse_text_response_to_learning_paths(text)

        # (선택) Pydantic 검증
        try:
            payload = LearningPathsPayload.model_validate(data)
            logger.info("학습 경로 파싱 완료",
                        path_count=len(payload.learning_paths),
                        genai_json_ok=1, genai_backup_parse_used=0)
            return [p.model_dump() for p in payload.learning_paths]
        except Exception as e:
            logger.warning("학습 경로 Pydantic 검증 실패 - 원시 JSON 사용",
                           err=str(e), genai_json_ok=1, genai_backup_parse_used=0)
            return data.get("learning_paths", [])

    # ---------- Prompt Builders (JSON-only 강제 규칙 포함) ----------
    def _create_interview_questions_prompt(self, career_summary: str, job_functions: str, technical_skills: str) -> str:
        return f"""
    당신은 경험이 풍부한 기술 면접관이다. 아래 구직자 정보를 바탕으로 실제 면접에서 사용할 수 있는 심층 질문 5개를 생성하라.

    [구직자 정보]
    - 경력 요약: {career_summary}
    - 수행 직무: {job_functions}
    - 보유 기술 스킬: {technical_skills}

    [출력 형식]
    반드시 **유효한 JSON만** 출력한다. 코드블록 금지. 주석/설명 금지. 후행 쉼표 금지.
    키 순서 유지. 문자열은 모두 따옴표 사용. 배열은 비어 있지 않게 생성.
    형식:
    {{
      "questions": [
        {{
          "question_text": "문장 끝이 반드시 물음표(?)로 끝나는 한국어 질문",
          "category": "기술역량 | 문제해결 | 협업경험 | 프로젝트경험 | 성장가능성 중 하나",
          "difficulty": "초급 | 중급 | 고급 중 하나",
          "reasoning": "출제 의도 및 평가 포인트를 1~2문장으로 요약"
        }}
      ]
    }}

    [강제 규칙]
    - 총 5문항이며 난이도 분포는 정확히: 초급 1개, 중급 2개, 고급 2개.
    - 모든 질문은 한국어 자연어 문장으로, 실제 면접에서 구두로 질문 가능해야 한다.
    - 질문 간 주제 중복 금지(각기 다른 역량·상황을 평가하도록 구성).
    - "question_text"는 반드시 물음표로 끝난다.
    - "category"와 "difficulty"는 위 허용값만 사용한다(다른 값 금지).
    - 특정 회사명/내부명/비속어/개인식별정보 포함 금지.

    [출력 예시 — 값은 예시일 뿐이며 반드시 위 구직자 정보에 맞게 재생성할 것]
    {{
      "questions": [
        {{
          "question_text": "MSA 환경에서 서비스 간 통신 방식(REST, 메시지 큐 등)을 선택할 때 어떤 기준으로 결정했으며 그 장단점은 무엇입니까?",
          "category": "기술역량",
          "difficulty": "초급",
          "reasoning": "서비스 간 통신 방식 선택 경험과 트레이드오프 이해를 평가"
        }}
      ]
    }}
    """

    def _create_learning_path_prompt(self, career_summary: str, job_functions: str, technical_skills: str) -> str:
        return f"""
    당신은 개발자 커리어 코치다. 아래 구직자 정보를 바탕으로 개인화된 학습 경로 3~5개를 제안하라.

    [구직자 정보]
    - 경력 요약: {career_summary}
    - 수행 직무: {job_functions}
    - 보유 기술 스킬: {technical_skills}

    [설계 기준]
    (1) 현 스택 심화, (2) 시장 신기술, (3) 부족 영역 보완, (4) 소프트스킬, (5) 장기 전략 중 최소 3개 이상을 포괄.
    각 경로는 실행 가능하고 측정 가능한 단계(action_items)를 포함한다.

    [출력 형식]
    반드시 **유효한 JSON만** 출력한다. 코드블록 금지. 주석/설명 금지. 후행 쉼표 금지.
    키 순서 유지. 문자열은 모두 따옴표 사용.
    형식:
    {{
      "learning_paths": [
        {{
          "title": "10~20자 내외의 명확한 학습 주제",
          "description": "2~3문장으로 핵심 목표와 적용 상황 요약",
          "priority": "높음 | 중간 | 낮음 중 하나",
          "estimated_duration": "숫자+단위 (예: '4주', '3개월')",
          "difficulty": "초급 | 중급 | 고급 중 하나",
          "action_items": [
            {{"step": 1, "action": "구체적 활동(동사로 시작)", "duration": "예: '1주'", "resources": ["최소 1개 자료(URL 포함)"]}},
            {{"step": 2, "action": "구체적 활동", "duration": "예: '1주'", "resources": ["최소 1개 자료(URL 포함)"]}}
          ],
          "recommended_resources": [
            {{"type": "책 | 강의 | 문서 | 블로그", "title": "자료명", "url": "https://도메인/...","description": "간단한 요약"}}
          ],
          "expected_outcomes": ["명사형 결과 항목 1", "명사형 결과 항목 2"]
        }}
      ]
    }}

    [강제 규칙]
    - "learning_paths"는 3~5개 생성.
    - 각 경로의 "action_items"는 최소 2개 이상, 실제 수행 가능한 단계로 작성.
    - "estimated_duration"은 반드시 숫자+단위 형식(예: 2주, 6주, 3개월).
    - "priority"와 "difficulty"는 허용값만 사용.
    - 모든 resources는 URL 포함.
    - 동일/유사 주제의 중복 경로 생성 금지.
    - 개인식별정보/회사 내부 비밀/비속어 금지.

    [출력 예시 — 값은 예시일 뿐이며 반드시 위 구직자 정보에 맞게 재생성할 것]
    {{
      "learning_paths": [
        {{
          "title": "클라우드 네이티브 및 쿠버네티스 심화",
          "description": "쿠버네티스 및 클라우드 네이티브 아키텍처를 학습하여 Spring Boot MSA를 효율적으로 배포/운영한다.",
          "priority": "높음",
          "estimated_duration": "10주",
          "difficulty": "중급",
          "action_items": [
            {{"step": 1, "action": "AWS EKS에 클러스터 구축 및 애플리케이션 배포 자동화", "duration": "4주", "resources": ["https://kubernetes.io/docs/home/", "https://docs.aws.amazon.com/eks/"]}},
            {{"step": 2, "action": "Helm 차트를 사용해 MSA 서비스 패키징 및 GitOps 적용", "duration": "3주", "resources": ["https://helm.sh/docs/", "https://argo-cd.readthedocs.io/"]}}
          ],
          "recommended_resources": [
            {{"type": "책", "title": "Kubernetes in Action", "url": "https://www.manning.com/books/kubernetes-in-action-second-edition", "description": "쿠버네티스 핵심 개념과 실전 활용"}}
          ],
          "expected_outcomes": ["쿠버네티스 기반 CI/CD 구현 역량", "클라우드 네이티브 운영 자동화 경험"]
        }}
      ]
    }}
    """
    # ---------- Extraction / Cleaning ----------
    def _extract_response_text(self, response) -> str:
        """
        항상 str 반환.
        1) 구조화 출력이면 response.text
        2) 멀티파트: candidates[].content.parts[].text 수집
        3) 최후수단: to_dict() 직렬화
        """
        t = getattr(response, "text", None)
        if isinstance(t, str) and t.strip():
            return t.strip()

        texts: List[str] = []
        for cand in getattr(response, "candidates", []) or []:
            content = getattr(cand, "content", None)
            parts = getattr(content, "parts", []) if content else []
            for part in parts:
                pt = getattr(part, "text", None)
                if isinstance(pt, str) and pt.strip():
                    texts.append(pt.strip())

        if texts:
            return "\n".join(texts)

        if hasattr(response, "to_dict"):
            try:
                return json.dumps(response.to_dict(), ensure_ascii=False)
            except Exception:
                pass

        return ""

    def _clean_json_response(self, s: str) -> str:
        """
        코드블록/잡설 제거 + 첫/마지막 JSON 토큰으로 슬라이스
        """
        if not isinstance(s, str):
            return ""
        # 코드블록 제거
        s = re.sub(r"```json\s*(.*?)\s*```", r"\1", s, flags=re.DOTALL | re.IGNORECASE)
        s = re.sub(r"```\s*(.*?)\s*```", r"\1", s, flags=re.DOTALL)
        # BOM/제로폭 등 정리
        s = s.replace("\ufeff", "").replace("\u200b", "").strip()

        # 첫 { 와 마지막 }를 기준으로 잘라 JSON 본문만 추출
        start = s.find("{")
        end = s.rfind("}")
        if start != -1 and end != -1 and end > start:
            s = s[start:end+1].strip()

        return s

    # ---------- Fallback Parsers ----------
    def _parse_text_response_to_questions(self, text: str) -> List[Dict[str, Any]]:
        out: List[Dict[str, Any]] = []
        for line in text.splitlines():
            line = line.strip()
            if not line:
                continue
            if ("?" in line) or ("질문" in line) or ("Question" in line):
                out.append({
                    "question_text": line,
                    "category": "기술역량",
                    "difficulty": "중급",
                    "reasoning": "자동 파싱"
                })
            if len(out) >= 5:
                break
        return out

    def _parse_text_response_to_learning_paths(self, text: str) -> List[Dict[str, Any]]:
        paths: List[Dict[str, Any]] = []
        current: Optional[Dict[str, Any]] = None

        def new_path(title: str) -> Dict[str, Any]:
            return {
                "title": title[:120],
                "description": "자동 파싱된 학습 경로",
                "priority": "중간",
                "estimated_duration": "1-3개월",
                "difficulty": "중급",
                "action_items": [{"step": 1, "action": title[:120], "duration": "1주", "resources": []}],
                "recommended_resources": [],
                "expected_outcomes": []
            }

        for raw in text.splitlines():
            line = raw.strip()
            if not line:
                continue
            if ("학습" in line) or ("경로" in line) or ("Learning" in line) or ("Path" in line):
                if current:
                    paths.append(current)
                current = new_path(line)

        if current:
            paths.append(current)

        return paths[:5]


# Singleton
gemini_service = GeminiService()