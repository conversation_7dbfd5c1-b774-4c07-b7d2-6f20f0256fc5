# 데이터베이스 설정
DATABASE_URL=postgresql+asyncpg://jobkorea:jobkorea_pass@localhost:5432/jobkorea_ai

# Redis 설정
REDIS_URL=redis://localhost:6379/0

# RabbitMQ 설정
RABBITMQ_URL=amqp://jobkorea:jobkorea_rabbit@localhost:5672/jobkorea_vhost

# AI 서비스 설정
GEMINI_API_KEY=your_gemini_api_key_here

# 애플리케이션 설정
LOG_LEVEL=INFO
DEBUG=False
SECRET_KEY=your-secret-key-here

# 보안 설정
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# 외부 서비스 제한
GEMINI_TIMEOUT_SECONDS=30
GEMINI_MAX_RETRIES=3
RATE_LIMIT_PER_MINUTE=60
