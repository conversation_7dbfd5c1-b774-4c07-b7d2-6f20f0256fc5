# Redis 설정 파일

# 네트워크 설정
bind 0.0.0.0
port 6379
timeout 300
tcp-keepalive 60

# 메모리 설정
maxmemory 512mb
maxmemory-policy allkeys-lru

# 영속성 설정
save 900 1
save 300 10
save 60 10000

# AOF 설정
appendonly yes
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 로그 설정
loglevel notice
logfile ""

# 클라이언트 설정
maxclients 10000

# 보안 설정 (개발 환경용)
protected-mode no

# 슬로우 로그 설정
slowlog-log-slower-than 10000
slowlog-max-len 128

# 키 만료 설정
hz 10

