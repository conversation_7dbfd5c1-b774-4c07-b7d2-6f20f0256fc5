# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/

# Environment Variables
.env
.env.local
.env.production
.env.staging

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# MacOS
.DS_Store

# Logs
*.log
logs/

# Database
*.db
*.sqlite3

# Docker
.docker/

# SSL Certificates
nginx/ssl/*.pem
nginx/ssl/*.key
nginx/ssl/*.crt

# Cache
.cache/
.pytest_cache/

# Coverage
htmlcov/
.tox/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery
celerybeat-schedule
celerybeat.pid

# Alembic
alembic/versions/*.py

# Temporary files
tmp/
temp/
